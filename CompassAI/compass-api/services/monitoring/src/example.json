[{
  "sqlQuery": "SELECT \"t\".* FROM \"TransactionForAiQuery\" AS \"t\" LEFT JOIN \"LeafCategoryForAiQuery\" AS \"lc\" ON \"t\".\"leafCategoryId\" = \"lc\".\"id\" WHERE \"lc\".\"id\" IS NULL",
  "reasoning": "The user wants to be notified when a new transaction is uncategorized. This requires monitoring all transactions for missing category assignments.",
  "instructions": [
    "This alert runs continuously, checking each new transaction as it's created or modified.",
    "It triggers automatically when any transaction is saved without a category.",
    "The alert monitors all transactions across all accounts for missing categorization.",
    "When viewing the alert, you'll see a list of all uncategorized transactions.",
    "Use this alert to ensure all transactions are properly categorized for accurate financial reporting and analysis.",
    "When notified, review the uncategorized transaction(s) and assign appropriate categories promptly."
  ],
  "notification": {
    "notificationQuery": "SELECT SUM(ABS(CAST(\"t\".\"convertedAmount\" ->> 'EUR' AS DECIMAL))) AS \"total_amount\", 'EUR' AS \"currency\" FROM \"TransactionForAiQuery\" AS \"t\" LEFT JOIN \"LeafCategoryForAiQuery\" AS \"lc\" ON \"t\".\"leafCategoryId\" = \"lc\".\"id\" WHERE \"lc\".\"id\" IS NULL",
    "notificationMessage": "New uncategorized transaction(s) detected. Total uncategorized amount: **{total_amount} {currency}**."
  },
  "evaluationDay": [],
  "displayQueries": [
    {
      "displayQuerySql": "SELECT \"t\".* FROM \"TransactionForAiDisplayQuery\" AS \"t\" LEFT JOIN \"LeafCategoryForAiQuery\" AS \"lc\" ON \"t\".\"leafCategoryId\" = \"lc\".\"id\" WHERE \"lc\".\"id\" IS NULL",
      "displayQueryType": "transaction",
      "displayQueryTitle": "Uncategorized Transactions"
    }
  ],
  "evaluationFrequency": null
},
{
  "sqlQuery": "
    WITH \"current_month\" AS (
      SELECT COALESCE(SUM(ABS(CAST(\"t\".\"convertedAmount\" ->> 'EUR' AS DECIMAL))), 0) AS \"total\" 
      FROM \"TransactionForAiQuery\" AS \"t\" 
      WHERE \"t\".\"type\" = 'EXPENSE' 
      AND DATE_TRUNC('MONTH', \"t\".\"date\") = DATE_TRUNC('MONTH', CURRENT_DATE)
    ), 
    \"previous_month\" AS (
      SELECT COALESCE(SUM(ABS(CAST(\"t\".\"convertedAmount\" ->> 'EUR' AS DECIMAL))), 0) AS \"total\" 
      FROM \"TransactionForAiQuery\" AS \"t\" 
      WHERE \"t\".\"type\" = 'EXPENSE' 
      AND DATE_TRUNC('MONTH', \"t\".\"date\") = DATE_TRUNC('MONTH', CURRENT_DATE - INTERVAL '1 MONTH')
    ) 
    SELECT 
      \"current_month\".\"total\" AS \"current_total\", 
      \"previous_month\".\"total\" AS \"previous_total\" 
    FROM \"current_month\", \"previous_month\" 
    WHERE \"current_month\".\"total\" > \"previous_month\".\"total\" * 1.1
  ",
  "reasoning": "We need to create an alert that compares the total monthly expenses between the current month and the previous month, triggering when there's an increase of more than 10%.",
  "instructions": [
    "This alert is evaluated at the end of each month",
    "It compares the total expenses of the current month to the previous month",
    "The alert is triggered if the current month's expenses are more than 10% higher than the previous month",
    "All expense transactions for both the current and previous month are monitored",
    "When viewing the alert, you'll see two lists: current month's expenses and previous month's expenses",
    "Use this alert to quickly identify significant increases in monthly spending and take appropriate action"
  ],
  "notification": {
    "notificationQuery": "
      WITH \"current_month\" AS (
        SELECT SUM(ABS(CAST(\"t\".\"convertedAmount\" ->> 'EUR' AS DECIMAL))) AS \"total\" 
        FROM \"TransactionForAiQuery\" AS \"t\" 
        WHERE \"t\".\"type\" = 'EXPENSE' 
        AND DATE_TRUNC('MONTH', \"t\".\"date\") = DATE_TRUNC('MONTH', CURRENT_DATE)
      ), 
      \"previous_month\" AS (
        SELECT SUM(ABS(CAST(\"t\".\"convertedAmount\" ->> 'EUR' AS DECIMAL))) AS \"total\" 
        FROM \"TransactionForAiQuery\" AS \"t\" 
        WHERE \"t\".\"type\" = 'EXPENSE' 
        AND DATE_TRUNC('MONTH', \"t\".\"date\") = DATE_TRUNC('MONTH', CURRENT_DATE - INTERVAL '1 MONTH')
      ) 
      SELECT 
        \"current_month\".\"total\" AS \"current_total\",
        \"previous_month\".\"total\" AS \"previous_total\" 
      FROM \"current_month\", \"previous_month\"
    ",
    "notificationMessage": "Total expenses this month (**{current_total} EUR**) have exceeded last month's expenses (**{previous_total} EUR**) by more than 10%."
  },
  "evaluationDay": [-1],
  "displayQueries": [
    {
      "displayQuerySql": "SELECT \"t\".* FROM \"TransactionForAiDisplayQuery\" AS \"t\" WHERE \"t\".\"type\" = 'EXPENSE' AND DATE_TRUNC('MONTH', \"t\".\"date\") = DATE_TRUNC('MONTH', CURRENT_DATE)",
      "displayQueryType": "transaction",
      "displayQueryTitle": "Current Month Expenses"
    },
    {
      "displayQuerySql": "SELECT \"t\".* FROM \"TransactionForAiDisplayQuery\" AS \"t\" WHERE \"t\".\"type\" = 'EXPENSE' AND DATE_TRUNC('MONTH', \"t\".\"date\") = DATE_TRUNC('MONTH', CURRENT_DATE - INTERVAL '1 MONTH')",
      "displayQueryType": "transaction",
      "displayQueryTitle": "Previous Month Expenses"
    }
  ],
  "evaluationFrequency": "P1M"
}
]